<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- Basic permission, add others as needed -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:requestLegacyExternalStorage="true"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.PurgeMaster"
        tools:targetApi="31">

        <activity
            android:name=".ui.activity.SplashActivity"
            android:exported="true"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.PurgeMaster">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity
            android:name=".ui.activity.MainActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.activity.AudiosActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.activity.VideosActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.activity.ImagesActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.activity.JunkScanActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.activity.SecureBrowserActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.activity.CompletedActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.activity.MoreActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.activity.PrivacyAgreementActivity"
            android:screenOrientation="portrait" />
    </application>

</manifest>