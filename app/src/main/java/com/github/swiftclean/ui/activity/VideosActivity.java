package com.github.swiftclean.ui.activity;

import android.os.Bundle;
import android.util.Log;

import com.github.swiftclean.ui.viewmodel.MediaViewModel;

/**
 * 视频文件管理页面
 */
public class VideosActivity extends ImagesActivity {
    private static final String TAG = "VideosActivity";

    @Override
    protected int getMediaType() {
        return MediaViewModel.MEDIA_TYPE_VIDEO;
    }

    @Override
    protected long getFileSizeThreshold() {
        return 0; // 不过滤大小
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Log.d(TAG, "onCreate: MediaType=" + getMediaType());
    }

    @Override
    protected String getToolbarTitle() {
        return "Videos";
    }
}
