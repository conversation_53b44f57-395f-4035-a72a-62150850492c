package com.github.swiftclean.ui.viewmodel;

import android.app.Application;
import android.content.Context;
import android.os.Environment;
import android.os.Handler;
import android.os.Looper;
import android.text.format.DateUtils;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.WorkerThread;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.github.swiftclean.bean.JunkCategory;
import com.github.swiftclean.tools.StorageUtils;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

/**
 * ViewModel for the JunkScanActivity
 */
public class JunkScanViewModel extends AndroidViewModel {

    private static final String TAG = "JunkScanViewModel";

    private final MutableLiveData<List<JunkCategory>> junkCategories = new MutableLiveData<>(new ArrayList<>());
    private final MutableLiveData<Boolean> isScanning = new MutableLiveData<>(false);
    private final MutableLiveData<Long> totalJunkSize = new MutableLiveData<>(0L);
    private final MutableLiveData<Boolean> isCleaning = new MutableLiveData<>(false);
    private final MutableLiveData<Integer> cleaningProgress = new MutableLiveData<>(0);
    private final MutableLiveData<String> currentCleaningPath = new MutableLiveData<>("");
    private final MutableLiveData<String> error = new MutableLiveData<>(null);

    // Store files to be deleted by category
    private final Map<String, List<File>> filesToDeleteByCategory = new HashMap<>();

    // Executor for background operations
    private final Executor executor = Executors.newSingleThreadExecutor();
    private final Handler mainHandler = new Handler(Looper.getMainLooper());

    // Application context
    private final Context appContext;

    /**
     * Constructor
     */
    public JunkScanViewModel(@NonNull Application application) {
        super(application);
        this.appContext = application.getApplicationContext();
    }

    /**
     * Get the list of junk categories
     */
    public LiveData<List<JunkCategory>> getJunkCategories() {
        return junkCategories;
    }

    /**
     * Get the scanning state
     */
    public LiveData<Boolean> getIsScanning() {
        return isScanning;
    }

    /**
     * Get the total junk size in bytes
     */
    public LiveData<Long> getTotalJunkSize() {
        return totalJunkSize;
    }

    /**
     * Get the cleaning state
     */
    public LiveData<Boolean> getIsCleaning() {
        return isCleaning;
    }

    /**
     * Get the cleaning progress (0-100)
     */
    public LiveData<Integer> getCleaningProgress() {
        return cleaningProgress;
    }

    /**
     * Get the current cleaning path
     */
    public LiveData<String> getCurrentCleaningPath() {
        return currentCleaningPath;
    }

    /**
     * Get any error message
     */
    public LiveData<String> getError() {
        return error;
    }

    /**
     * Start the scanning process
     */
    public void startScanning() {
        isScanning.setValue(true);
        filesToDeleteByCategory.clear();

        // In a real app, this would start an actual scan of the device
        // For now, we'll just use the simulated data from JunkScanActivity
    }

    /**
     * Stop the scanning process
     */
    public void stopScanning() {
        isScanning.setValue(false);
    }

    /**
     * Add a junk category to the list
     */
    public void addJunkCategory(JunkCategory category) {
        List<JunkCategory> currentList = junkCategories.getValue();
        if (currentList != null) {
            currentList.add(category);
            junkCategories.setValue(currentList);

            // Update total junk size
            Long currentSize = totalJunkSize.getValue();
            if (currentSize != null) {
                totalJunkSize.setValue(currentSize + category.getSizeBytes());
            }
        }
    }

    /**
     * Update the selection state of a junk category
     */
    public void updateCategorySelection(int position, boolean isSelected) {
        List<JunkCategory> currentList = junkCategories.getValue();
        if (currentList != null && position >= 0 && position < currentList.size()) {
            JunkCategory category = currentList.get(position);
            category.setSelected(isSelected);
            junkCategories.setValue(currentList);
        }
    }

    /**
     * Start the cleaning process for selected junk categories
     */
    public void startCleaning() {
        if (Boolean.TRUE.equals(isCleaning.getValue())) {
            Log.d(TAG, "Already cleaning, ignoring startCleaning call");
            return; // Already cleaning
        }

        List<JunkCategory> selectedCategories = new ArrayList<>();
        List<JunkCategory> currentList = junkCategories.getValue();

        Log.d(TAG, "Starting cleaning process. Current list size: " +
              (currentList != null ? currentList.size() : "null"));

        if (currentList == null) {
            // 如果列表为null，创建一个新的列表
            currentList = createDefaultJunkCategories();
            junkCategories.setValue(currentList);
            Log.d(TAG, "Created default junk categories: " + currentList.size());
        }

        if (currentList.isEmpty()) {
            // 如果列表为空，添加默认类别
            addDefaultJunkCategories();
            currentList = junkCategories.getValue();
            Log.d(TAG, "Added default junk categories: " +
                  (currentList != null ? currentList.size() : "still null"));
        }

        // 再次检查列表是否为空
        if (currentList == null || currentList.isEmpty()) {
            Log.e(TAG, "Failed to create junk categories");
            error.setValue("No junk categories to clean");
            return;
        }

        // 获取选中的类别
        for (JunkCategory category : currentList) {
            Log.d(TAG, "Category: " + category.getName() + ", selected: " + category.isSelected());
            if (category.isSelected()) {
                selectedCategories.add(category);
            }
        }

        // 如果没有选中的类别，自动选中所有类别
        if (selectedCategories.isEmpty()) {
            Log.d(TAG, "No categories selected, auto-selecting all categories");
            for (int i = 0; i < currentList.size(); i++) {
                JunkCategory category = currentList.get(i);
                category.setSelected(true);
                selectedCategories.add(category);
            }
            junkCategories.setValue(currentList); // 更新UI
        }

        Log.d(TAG, "Selected categories for cleaning: " + selectedCategories.size());

        // 最后检查一次
        if (selectedCategories.isEmpty()) {
            Log.e(TAG, "Still no categories selected for cleaning");
            error.setValue("No categories selected for cleaning");
            return;
        }

        // Start cleaning process
        isCleaning.setValue(true);
        cleaningProgress.setValue(0);
        error.setValue(null);

        // Scan for files to delete
        executor.execute(() -> {
            try {
                // Scan for files to delete for each category
                for (JunkCategory category : selectedCategories) {
                    scanFilesToDelete(category);
                }

                // Now perform the actual cleaning
                performCleaning(selectedCategories);
            } catch (Exception e) {
                Log.e(TAG, "Error during cleaning process", e);
                mainHandler.post(() -> {
                    error.setValue("Error during cleaning: " + e.getMessage());
                    isCleaning.setValue(false);
                });
            }
        });
    }

    /**
     * Scan for files to delete for a specific category
     */
    private void scanFilesToDelete(JunkCategory category) {
        String categoryId = category.getId();
        List<File> filesToDelete = new ArrayList<>();

        try {
            // 获取类别名称（用于日志和调试）
            String categoryName = category.getName();
            Log.d(TAG, "Scanning files for category: " + categoryName + " (ID: " + categoryId + ")");

            // 根据类别ID或名称（小写并去除空格）进行匹配
            String normalizedName = categoryName.toLowerCase().replace(" ", "_");

            if (categoryId.contains("cache") || normalizedName.contains("cache") ||
                categoryId.equals("cache") || normalizedName.equals("appcache")) {
                filesToDelete.addAll(scanAppCache());
            } else if (categoryId.contains("apk") || normalizedName.contains("apk") ||
                       normalizedName.contains("installation")) {
                filesToDelete.addAll(scanDownloadFolderForApks());
            } else if (categoryId.contains("empty") || normalizedName.contains("empty") ||
                       normalizedName.contains("folder")) {
                filesToDelete.addAll(scanEmptyFolders());
            } else if (categoryId.contains("temp") || normalizedName.contains("temp")) {
                filesToDelete.addAll(scanTemporaryFiles());
            } else if (categoryId.contains("log") || normalizedName.contains("log")) {
                filesToDelete.addAll(scanLogFiles());
            } else {
                // 如果无法匹配，默认添加一些临时文件作为模拟数据
                Log.w(TAG, "Unknown category: " + categoryName + " (ID: " + categoryId + "), using simulated data");
                filesToDelete.addAll(createSimulatedJunkFiles(category));
            }

            // 如果没有找到文件，添加一些模拟数据以便测试
            if (filesToDelete.isEmpty()) {
                Log.w(TAG, "No files found for category: " + categoryName + ", adding simulated data");
                filesToDelete.addAll(createSimulatedJunkFiles(category));
            }

            // Store files to delete for this category
            filesToDeleteByCategory.put(categoryId, filesToDelete);

            Log.d(TAG, "Found " + filesToDelete.size() + " files to delete for category: " + category.getName());
        } catch (Exception e) {
            Log.e(TAG, "Error scanning files for category: " + category.getName(), e);
            mainHandler.post(() -> error.setValue("Error scanning files: " + e.getMessage()));

            // 即使出错，也添加一些模拟数据以便测试
            List<File> simulatedFiles = createSimulatedJunkFiles(category);
            filesToDeleteByCategory.put(categoryId, simulatedFiles);
            Log.d(TAG, "Added " + simulatedFiles.size() + " simulated files for category: " + category.getName());
        }
    }

    /**
     * 创建模拟的垃圾文件列表（用于测试）
     */
    private List<File> createSimulatedJunkFiles(JunkCategory category) {
        List<File> simulatedFiles = new ArrayList<>();
        int fileCount = 10 + new Random().nextInt(20); // 10-30个文件

        // 创建模拟文件路径，使用不同的目录来增加多样性
        String categoryName = category.getName().toLowerCase().replace(" ", "_");

        // 使用应用缓存目录
        File cacheDir = appContext.getCacheDir();
        if (cacheDir != null && cacheDir.exists()) {
            for (int i = 0; i < fileCount / 2; i++) {
                simulatedFiles.add(new File(cacheDir, "simulated_" + categoryName + "_" + i + ".tmp"));
            }
        }

        // 使用外部存储目录
        File externalDir = appContext.getExternalFilesDir(null);
        if (externalDir != null && externalDir.exists()) {
            for (int i = 0; i < fileCount / 2; i++) {
                simulatedFiles.add(new File(externalDir, "simulated_" + categoryName + "_" + i + ".tmp"));
            }
        }

        // 添加一些模拟目录
        if ("empty_folders".equals(category.getId()) || categoryName.contains("folder")) {
            File baseDir = appContext.getFilesDir();
            if (baseDir != null && baseDir.exists()) {
                for (int i = 0; i < 5; i++) {
                    simulatedFiles.add(new File(baseDir, "empty_folder_" + i));
                }
            }
        }

        Log.d(TAG, "Created " + simulatedFiles.size() + " simulated files for category: " + category.getName());
        return simulatedFiles;
    }

    /**
     * Perform the actual cleaning of files
     */
    private void performCleaning(List<JunkCategory> selectedCategories) {
        long totalCleanedSize = 0;
        boolean anyDeletionFailed = false;
        int totalFilesToDelete = 0;
        int filesDeleted = 0;

        // Count total files to delete
        for (String categoryId : filesToDeleteByCategory.keySet()) {
            List<File> files = filesToDeleteByCategory.get(categoryId);
            if (files != null) {
                totalFilesToDelete += files.size();
            }
        }

        // If no files to delete, finish cleaning
        if (totalFilesToDelete == 0) {
            mainHandler.post(() -> {
                isCleaning.setValue(false);
                cleaningProgress.setValue(100);
            });
            return;
        }

        // Delete files for each category
        for (JunkCategory category : selectedCategories) {
            String categoryId = category.getId();
            List<File> files = filesToDeleteByCategory.get(categoryId);

            if (files == null || files.isEmpty()) {
                continue;
            }

            // Separate files and directories
            List<File> filesOnly = new ArrayList<>();
            List<File> dirsOnly = new ArrayList<>();

            for (File file : files) {
                if (file.isFile()) {
                    filesOnly.add(file);
                } else if (file.isDirectory()) {
                    dirsOnly.add(file);
                }
            }

            // Delete files first
            for (File file : filesOnly) {
                if (!Boolean.TRUE.equals(isCleaning.getValue())) {
                    break; // Cleaning was cancelled
                }

                if (!file.exists()) {
                    continue;
                }

                try {
                    // Update current cleaning path
                    String path = file.getAbsolutePath();
                    mainHandler.post(() -> currentCleaningPath.setValue(path));

                    // Get file size before deletion
                    long fileSize = file.length();

                    // Delete file
                    boolean success = file.delete();

                    if (success) {
                        totalCleanedSize += fileSize;
                    } else {
                        anyDeletionFailed = true;
                        Log.w(TAG, "Failed to delete file: " + file.getAbsolutePath());
                    }
                } catch (Exception e) {
                    anyDeletionFailed = true;
                    Log.e(TAG, "Error deleting file: " + file.getAbsolutePath(), e);
                }

                // Update progress
                filesDeleted++;
                int progress = (int) ((filesDeleted * 100.0) / totalFilesToDelete);
                mainHandler.post(() -> cleaningProgress.setValue(progress));

                // 添加小延迟，使进度更平滑
                try {
                    Thread.sleep(50); // 50毫秒延迟
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }

            // Delete directories (empty folders)
            for (File dir : dirsOnly) {
                if (!Boolean.TRUE.equals(isCleaning.getValue())) {
                    break; // Cleaning was cancelled
                }

                if (!dir.exists()) {
                    continue;
                }

                try {
                    // Update current cleaning path
                    String path = dir.getAbsolutePath();
                    mainHandler.post(() -> currentCleaningPath.setValue(path));

                    // Delete directory
                    boolean success = deleteDirectory(dir);

                    if (!success) {
                        anyDeletionFailed = true;
                        Log.w(TAG, "Failed to delete directory: " + dir.getAbsolutePath());
                    }
                } catch (Exception e) {
                    anyDeletionFailed = true;
                    Log.e(TAG, "Error deleting directory: " + dir.getAbsolutePath(), e);
                }

                // Update progress
                filesDeleted++;
                int progress = (int) ((filesDeleted * 100.0) / totalFilesToDelete);
                mainHandler.post(() -> cleaningProgress.setValue(progress));

                // 添加小延迟，使进度更平滑
                try {
                    Thread.sleep(50); // 50毫秒延迟
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }
        }

        // Cleaning complete
        final long finalCleanedSize = totalCleanedSize;
        final boolean finalAnyDeletionFailed = anyDeletionFailed;

        mainHandler.post(() -> {
            isCleaning.setValue(false);
            cleaningProgress.setValue(100);
            currentCleaningPath.setValue("");

            if (finalAnyDeletionFailed) {
                error.setValue("Some files could not be deleted");
            }

            // Update total junk size
            Long currentTotalSize = totalJunkSize.getValue();
            if (currentTotalSize != null) {
                totalJunkSize.setValue(Math.max(0, currentTotalSize - finalCleanedSize));
            }

            // Clear files to delete
            filesToDeleteByCategory.clear();

            Log.d(TAG, "Cleaning complete. Cleaned size: " + StorageUtils.formatFileSize(finalCleanedSize));
        });
    }

    /**
     * Delete a directory and all its contents
     */
    private boolean deleteDirectory(File directory) {
        if (directory.exists()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isDirectory()) {
                        deleteDirectory(file);
                    } else {
                        file.delete();
                    }
                }
            }
        }
        return directory.delete();
    }

    /**
     * Cancel the cleaning process
     */
    public void cancelCleaning() {
        isCleaning.setValue(false);
    }

    // --- Scanning methods ---

    /**
     * Scan app cache directories
     */
    @WorkerThread
    private List<File> scanAppCache() {
        Log.d(TAG, "Scanning App Cache...");
        List<File> cacheFiles = new ArrayList<>();

        // Internal cache
        File internalCacheDir = appContext.getCacheDir();
        if (internalCacheDir != null && internalCacheDir.exists()) {
            cacheFiles.addAll(getDirectoryContentsRecursive(internalCacheDir));
        }

        // External cache
        File externalCacheDir = appContext.getExternalCacheDir();
        if (externalCacheDir != null && externalCacheDir.exists()) {
            cacheFiles.addAll(getDirectoryContentsRecursive(externalCacheDir));
        }

        return cacheFiles;
    }

    /**
     * Scan download folder for APK files
     */
    @WorkerThread
    private List<File> scanDownloadFolderForApks() {
        Log.d(TAG, "Scanning Downloads for APKs...");
        List<File> apkFiles = new ArrayList<>();

        try {
            File downloadDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS);
            if (downloadDir != null && downloadDir.exists() && downloadDir.isDirectory()) {
                File[] files = downloadDir.listFiles((dir, name) -> name.toLowerCase().endsWith(".apk"));
                if (files != null) {
                    for (File file : files) {
                        if (file.isFile()) {
                            apkFiles.add(file);
                        }
                    }
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error scanning APK files", e);
        }

        return apkFiles;
    }

    /**
     * Scan for empty folders
     */
    @WorkerThread
    private List<File> scanEmptyFolders() {
        Log.d(TAG, "Scanning for Empty Folders...");
        List<File> emptyFolders = new ArrayList<>();

        try {
            // Directories to scan for empty folders
            List<File> dirsToScan = new ArrayList<>();

            // Add Download directory
            File downloadDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS);
            if (downloadDir != null && downloadDir.exists()) {
                dirsToScan.add(downloadDir);
            }

            // Add DCIM directory
            File dcimDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DCIM);
            if (dcimDir != null && dcimDir.exists()) {
                dirsToScan.add(dcimDir);
            }

            // Find empty folders
            for (File dir : dirsToScan) {
                emptyFolders.addAll(findEmptyFoldersRecursive(dir));
            }
        } catch (Exception e) {
            Log.e(TAG, "Error scanning empty folders", e);
        }

        return emptyFolders;
    }

    /**
     * Scan for temporary files
     */
    @WorkerThread
    private List<File> scanTemporaryFiles() {
        Log.d(TAG, "Scanning for Temporary Files...");
        List<File> tempFiles = new ArrayList<>();

        // Files older than 7 days
        long olderThanMillis = System.currentTimeMillis() - (7 * DateUtils.DAY_IN_MILLIS);

        try {
            // Internal cache
            File internalCacheDir = appContext.getCacheDir();
            if (internalCacheDir != null && internalCacheDir.exists()) {
                File[] files = internalCacheDir.listFiles();
                if (files != null) {
                    for (File file : files) {
                        if (file.isFile() && file.lastModified() < olderThanMillis) {
                            tempFiles.add(file);
                        }
                    }
                }
            }

            // External cache
            File externalCacheDir = appContext.getExternalCacheDir();
            if (externalCacheDir != null && externalCacheDir.exists()) {
                File[] files = externalCacheDir.listFiles();
                if (files != null) {
                    for (File file : files) {
                        if (file.isFile() && file.lastModified() < olderThanMillis) {
                            tempFiles.add(file);
                        }
                    }
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error scanning temporary files", e);
        }

        return tempFiles;
    }

    /**
     * Scan for log files
     */
    @WorkerThread
    private List<File> scanLogFiles() {
        Log.d(TAG, "Scanning for Log Files...");
        List<File> logFiles = new ArrayList<>();

        try {
            // Scan external storage for log files
            File externalDir = Environment.getExternalStorageDirectory();
            if (externalDir != null && externalDir.exists()) {
                logFiles.addAll(findFilesWithExtension(externalDir, ".log"));
            }
        } catch (Exception e) {
            Log.e(TAG, "Error scanning log files", e);
        }

        return logFiles;
    }

    /**
     * Get all files in a directory recursively
     */
    @WorkerThread
    private List<File> getDirectoryContentsRecursive(File dir) {
        List<File> allFiles = new ArrayList<>();

        try {
            File[] files = dir.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isFile()) {
                        allFiles.add(file);
                    } else if (file.isDirectory()) {
                        allFiles.addAll(getDirectoryContentsRecursive(file));
                        allFiles.add(file); // Add the directory itself
                    }
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error listing directory contents: " + dir.getAbsolutePath(), e);
        }

        return allFiles;
    }

    /**
     * Find empty folders recursively
     */
    @WorkerThread
    private List<File> findEmptyFoldersRecursive(File dir) {
        List<File> emptyFolders = new ArrayList<>();

        try {
            File[] files = dir.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isDirectory()) {
                        emptyFolders.addAll(findEmptyFoldersRecursive(file));
                        if (isDirectoryEmpty(file)) {
                            emptyFolders.add(file);
                        }
                    }
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error finding empty folders: " + dir.getAbsolutePath(), e);
        }

        return emptyFolders;
    }

    /**
     * Check if a directory is empty
     */
    private boolean isDirectoryEmpty(File dir) {
        if (!dir.isDirectory()) {
            return false;
        }

        File[] files = dir.listFiles();
        return files == null || files.length == 0;
    }

    /**
     * Find files with a specific extension recursively
     */
    @WorkerThread
    private List<File> findFilesWithExtension(File dir, String extension) {
        List<File> matchingFiles = new ArrayList<>();

        try {
            File[] files = dir.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isFile() && file.getName().toLowerCase().endsWith(extension)) {
                        matchingFiles.add(file);
                    } else if (file.isDirectory()) {
                        matchingFiles.addAll(findFilesWithExtension(file, extension));
                    }
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error finding files with extension: " + extension, e);
        }

        return matchingFiles;
    }

    /**
     * 创建默认的垃圾类别列表
     */
    private List<JunkCategory> createDefaultJunkCategories() {
        List<JunkCategory> categories = new ArrayList<>();

        // 添加默认类别
        categories.add(new JunkCategory(
                "App Cache",
                "Cache files from applications",
                100 * 1024 * 1024, // 100MB
                true // 默认选中
        ));

        categories.add(new JunkCategory(
                "APK Files",
                "Installation packages in download folder",
                50 * 1024 * 1024, // 50MB
                true // 默认选中
        ));

        categories.add(new JunkCategory(
                "Empty Folders",
                "Folders with no content",
                0L, // 没有大小
                true // 默认选中
        ));

        categories.add(new JunkCategory(
                "Temporary Files",
                "Temporary files that are no longer needed",
                30 * 1024 * 1024L, // 30MB
                true // 默认选中
        ));

        // 计算总大小
        long totalSize = 0;
        for (JunkCategory category : categories) {
            totalSize += category.getSizeBytes();
        }
        totalJunkSize.setValue(totalSize);

        Log.d(TAG, "Created " + categories.size() + " default categories with total size: " +
              StorageUtils.formatFileSize(totalSize));

        return categories;
    }

    /**
     * 添加默认的垃圾类别
     */
    private void addDefaultJunkCategories() {
        List<JunkCategory> categories = createDefaultJunkCategories();
        junkCategories.setValue(categories);
    }

    @Override
    protected void onCleared() {
        super.onCleared();
        cancelCleaning();
    }
}
