package com.github.swiftclean.ui.activity;

import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.core.view.WindowCompat;
import androidx.core.view.WindowInsetsControllerCompat;

import com.github.swiftclean.R;

public class MoreActivity extends AppCompatActivity {

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_more);

        // Set status bar icons to dark for Android M and above
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            WindowInsetsControllerCompat controller = new WindowInsetsControllerCompat(getWindow(), getWindow().getDecorView());
            // Set status bar icons to dark
            controller.setAppearanceLightStatusBars(true);
        }

        // Configure window for edge-to-edge display
        WindowCompat.setDecorFitsSystemWindows(getWindow(), false);
        
        // Set status bar color to white
        getWindow().setStatusBarColor(Color.WHITE);

        // Set up toolbar
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setDisplayShowHomeEnabled(true);
        }

        // Set toolbar navigation click listener
        toolbar.setNavigationOnClickListener(v -> onBackPressed());

        // Get app version name
        String versionName = "";
        try {
            versionName = getPackageManager().getPackageInfo(getPackageName(), 0).versionName;
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        }
        
        // Set version info text (commented out in original code)
        // TextView versionInfoTextView = findViewById(R.id.versionInfo);
        // versionInfoTextView.setText("版本 " + versionName);

        // Set click listener for privacy button
        View privacyButtonEnglish = findViewById(R.id.privacy_button_english);
        privacyButtonEnglish.setOnClickListener(v -> {
            startActivity(new Intent(MoreActivity.this, PrivacyAgreementActivity.class));
        });
    }
}
