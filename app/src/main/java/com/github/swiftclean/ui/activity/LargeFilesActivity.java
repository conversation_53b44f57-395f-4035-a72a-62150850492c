package com.github.swiftclean.ui.activity;

import com.github.swiftclean.ui.viewmodel.MediaViewModel;

/**
 * 大文件管理页面
 */
public class LargeFilesActivity extends FileActivity {

    @Override
    protected int getMediaType() {
        return MediaViewModel.MEDIA_TYPE_LARGE_FILES;
    }

    @Override
    protected long getFileSizeThreshold() {
        return 5 * 1024 * 1024L; // 5MB
    }

    @Override
    protected String getToolbarTitle() {
        return "Large Files";
    }
}
