package com.github.swiftclean.ui.activity;

import android.os.Build;
import android.os.Bundle;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.core.view.WindowInsetsControllerCompat;

/**
 * @PackageName : com.github.purgemaster.ui
 * <AUTHOR> Waiting
 * @Date : 2025/5/25 22:33
 * @Description :
 */
class BaseActivity extends AppCompatActivity {

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            WindowInsetsControllerCompat controllerCompat = new WindowInsetsControllerCompat(getWindow(), getWindow().getDecorView());
            controllerCompat.setAppearanceLightStatusBars(true);
        }
    }

    /**
     * Set up toolbar with back button
     */
    protected void setupToolbar(Toolbar toolbar) {
        setupToolbar(toolbar, null);
    }

    /**
     * Set up toolbar with back button and custom title
     */
    protected void setupToolbar(Toolbar toolbar, String titleText) {
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setDisplayShowHomeEnabled(false);
        }

        if (titleText != null) {
            toolbar.setTitle(titleText);
        }

        toolbar.setNavigationOnClickListener(view -> finish());
    }
}
