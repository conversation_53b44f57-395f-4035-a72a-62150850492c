package com.github.swiftclean.ui.viewmodel;

import android.app.Application;
import android.os.Environment;
import android.os.StatFs;

import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.github.swiftclean.tools.DisplayFormatters;

import java.io.File;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * Home ViewModel - Manages home screen data and storage information
 * Handles storage monitoring and provides data for the home screen UI
 */
public class HomeViewModel extends AndroidViewModel {

    private final MutableLiveData<MainScreenState> uiState = new MutableLiveData<>(new MainScreenState());
    private final MutableLiveData<Long> navigateToCleanupResult = new MutableLiveData<>();

    private final ScheduledExecutorService storageMonitorExecutor = Executors.newSingleThreadScheduledExecutor();

    public HomeViewModel(@NonNull Application application) {
        super(application);
        initializeStorageMonitoring();
    }

    // ==================== Public API Methods ====================

    /**
     * Get the current UI state
     * @return LiveData containing the main screen state
     */
    public LiveData<MainScreenState> getUiState() {
        return uiState;
    }

    /**
     * Get navigation events to cleanup result screen
     * @return LiveData containing cleanup result data
     */
    public LiveData<Long> getNavigateToCleanupResult() {
        return navigateToCleanupResult;
    }

    // ==================== Initialization Methods ====================

    private void initializeStorageMonitoring() {
        refreshStorageInformation();
        schedulePeriodicStorageUpdates();
    }

    private void schedulePeriodicStorageUpdates() {
        storageMonitorExecutor.scheduleAtFixedRate(
            this::refreshStorageInformation, 
            30, 30, TimeUnit.SECONDS
        );
    }

    // ==================== Storage Information Methods ====================

    private void refreshStorageInformation() {
        try {
            StorageInfo storageInfo = calculateStorageInfo();
            updateUiStateWithStorageInfo(storageInfo);
            logStorageInformation(storageInfo);
        } catch (Exception e) {
            handleStorageInfoError(e);
        }
    }

    private StorageInfo calculateStorageInfo() {
        File externalStorageDirectory = Environment.getExternalStorageDirectory();
        StatFs statFs = new StatFs(externalStorageDirectory.getPath());

        long blockSize = statFs.getBlockSizeLong();
        long totalBlocks = statFs.getBlockCountLong();
        long availableBlocks = statFs.getAvailableBlocksLong();

        long totalSize = totalBlocks * blockSize;
        long availableSize = availableBlocks * blockSize;
        long usedSize = totalSize - availableSize;

        float usagePercentage = calculateUsagePercentage(usedSize, totalSize);
        int usedGB = convertBytesToGB(usedSize);
        int totalGB = convertBytesToGB(totalSize);

        return new StorageInfo(usagePercentage, usedGB, totalGB);
    }

    private float calculateUsagePercentage(long usedSize, long totalSize) {
        if (totalSize == 0) return 0f;
        float percentage = ((float) usedSize / totalSize) * 100f;
        return Math.max(0f, Math.min(100f, percentage));
    }

    private int convertBytesToGB(long bytes) {
        return (int) (bytes / (1024L * 1024L * 1024L));
    }

    private void updateUiStateWithStorageInfo(StorageInfo storageInfo) {
        MainScreenState currentState = uiState.getValue();
        if (currentState != null) {
            MainScreenState newState = new MainScreenState(
                storageInfo.usagePercentage,
                storageInfo.usedGB,
                storageInfo.totalGB,
                currentState.isScanning()
            );
            uiState.postValue(newState);
        }
    }

    private void logStorageInformation(StorageInfo storageInfo) {
        android.util.Log.d("HomeViewModel", 
            String.format("Storage stats: %.1f%%, %dGB/%dGB", 
                storageInfo.usagePercentage, storageInfo.usedGB, storageInfo.totalGB));
    }

    private void handleStorageInfoError(Exception e) {
        android.util.Log.e("HomeViewModel", "Error updating storage info: " + e.getMessage(), e);
    }

    // ==================== Lifecycle Management ====================

    @Override
    protected void onCleared() {
        super.onCleared();
        shutdownStorageMonitoring();
    }

    private void shutdownStorageMonitoring() {
        if (storageMonitorExecutor != null && !storageMonitorExecutor.isShutdown()) {
            storageMonitorExecutor.shutdown();
        }
    }

    // ==================== Data Classes ====================

    /**
     * Internal storage information holder
     */
    private static class StorageInfo {
        final float usagePercentage;
        final int usedGB;
        final int totalGB;

        StorageInfo(float usagePercentage, int usedGB, int totalGB) {
            this.usagePercentage = usagePercentage;
            this.usedGB = usedGB;
            this.totalGB = totalGB;
        }
    }

    /**
     * Main screen state data class
     */
    public static class MainScreenState {
        private final float usagePercentage;
        private final int storageUsedGb;
        private final int storageTotalGb;
        private final boolean scanning;

        public MainScreenState() {
            this(0f, 0, 0, false);
        }

        public MainScreenState(float usagePercentage, int storageUsedGb, int storageTotalGb, boolean scanning) {
            this.usagePercentage = usagePercentage;
            this.storageUsedGb = storageUsedGb;
            this.storageTotalGb = storageTotalGb;
            this.scanning = scanning;
        }

        // ==================== Getters ====================

        public float getUsagePercentage() {
            return usagePercentage;
        }

        public int getStorageUsedGb() {
            return storageUsedGb;
        }

        public int getStorageTotalGb() {
            return storageTotalGb;
        }

        public boolean isScanning() {
            return scanning;
        }

        /**
         * Get formatted storage usage string
         * @return formatted string showing used/total storage
         */
        public String getFormattedStorageUsage() {
            long usedBytes = storageUsedGb * 1024L * 1024L * 1024L;
            long totalBytes = storageTotalGb * 1024L * 1024L * 1024L;
            return DisplayFormatters.formatStorageSize(usedBytes) + " / " + 
                   DisplayFormatters.formatStorageSize(totalBytes);
        }
    }
}
