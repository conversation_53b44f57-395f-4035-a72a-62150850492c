package com.github.swiftclean.ui.activity;

import android.os.Bundle;
import android.util.Log;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.GridLayoutManager;

import com.github.swiftclean.adapter.FileGridAdapter;
import com.github.swiftclean.bean.SelectableFile;
import com.github.swiftclean.ui.viewmodel.MediaViewModel;

/**
 * 图片文件管理页面 - 使用网格布局
 */
public class ImagesActivity extends FileActivity {
    private static final String TAG = "ImagesActivity";
    private FileGridAdapter imageAdapter;

    @Override
    protected int getMediaType() {
        return MediaViewModel.MEDIA_TYPE_IMAGE;
    }

    @Override
    protected long getFileSizeThreshold() {
        return 0; // 不过滤大小
    }

    @Override
    protected String getToolbarTitle() {
        return "Images";
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Log.d(TAG, "onCreate: MediaType=" + getMediaType());

        // 使用网格布局替换默认的列表布局
        setupGridLayout();
    }

    /**
     * 设置网格布局
     */
    private void setupGridLayout() {
        // 创建图片网格适配器
        imageAdapter = new FileGridAdapter(new FileGridAdapter.OnImageItemClickListener() {
            @Override
            public void onImageItemClick(SelectableFile file) {
                // 处理图片点击事件
                viewModel.toggleItemSelection(file);
                imageAdapter.notifyDataSetChanged();
                updateUI();
            }
        });

        // 使用网格布局管理器
        GridLayoutManager gridLayoutManager = new GridLayoutManager(this, 3);
        recyclerView.setLayoutManager(gridLayoutManager);

        // 设置适配器
        recyclerView.setAdapter(imageAdapter);

        // 观察文件数据变化
        viewModel.getFiles().observe(this, files -> {
            imageAdapter.updateFiles(files);
            updateUI();
        });
    }

    /**
     * 重写此方法以防止父类的FileAdapter被使用
     */
    @Override
    protected void setupRecyclerView() {
        // 不调用super.setupRecyclerView()，因为我们不想使用FileAdapter
        // 我们将在setupGridLayout()中设置自己的适配器
    }

    /**
     * 重写此方法以使用imageAdapter而不是adapter
     */
    @Override
    protected void setupBottomBar() {
        selectAllCheckBox.setOnClickListener(v -> {
            viewModel.toggleSelectAll();
            imageAdapter.notifyDataSetChanged(); // 通知适配器数据已更改
            updateUI(); // 更新UI状态
        });

        selectAllButton.setOnClickListener(v -> {
            viewModel.toggleSelectAll();
            imageAdapter.notifyDataSetChanged(); // 通知适配器数据已更改
            updateUI(); // 更新UI状态
        });
    }

    /**
     * 重写此方法以使用自定义的观察逻辑
     */
    @Override
    protected void observeViewModel() {
        // 观察文件数据变化
        viewModel.getFiles().observe(this, files -> {
            imageAdapter.updateFiles(files);
            updateUI();
        });

        // 观察加载状态
        viewModel.getIsLoading().observe(this, isLoading -> {
            progressBar.setVisibility(isLoading ? View.VISIBLE : View.GONE);
            updateUI();
        });

        // 观察错误状态
        viewModel.getError().observe(this, error -> {
            if (error != null) {
                errorMessageText.setText(error);
            }
            updateUI();
        });

        // 观察权限状态
        viewModel.getPermissionGranted().observe(this, granted -> {
            updateUI();
        });

        // 观察权限对话框状态
        viewModel.getShowPermissionDialog().observe(this, show -> {
            // 使用父类的方法处理权限对话框
            // 不需要在这里处理，父类会处理
        });
    }
}
