package com.github.swiftclean.ui.activity;

import android.Manifest;
import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ValueAnimator;
import android.app.Dialog;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.RectF;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.view.animation.LinearInterpolator;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.content.ContextCompat;
import androidx.core.view.WindowInsetsControllerCompat;
import androidx.lifecycle.ViewModelProvider;

import com.github.swiftclean.R;
import com.github.swiftclean.databinding.ActivityHomeBinding;
import com.github.swiftclean.ui.navigation.Screen;
import com.github.swiftclean.ui.viewmodel.HomeViewModel;
import com.github.swiftclean.ui.views.CustomDrawView;

/**
 * Home Activity - Main entry point of the application
 * Displays storage usage, cleanup options and navigation to different features
 */
public class HomeActivity extends AppCompatActivity {

    private ActivityHomeBinding binding;
    private HomeViewModel viewModel;

    // Progress animation variables
    private ValueAnimator progressAnimator;
    private float currentSweepAngle = 0f;
    private Paint progressPaint;
    private RectF progressRect;
    private boolean isAnimating = false;

    // Permission handling
    private ActivityResultLauncher<String[]> permissionLauncher;
    private String[] requiredPermissions;
    private Class<?> pendingActivityClass;

    // ==================== Lifecycle Methods ====================

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initializeActivity();
    }

    @Override
    protected void onResume() {
        super.onResume();
        refreshStorageDisplay();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        cleanupResources();
    }

    // ==================== Initialization Methods ====================

    private void initializeActivity() {
        setupViewBinding();
        configureStatusBar();
        initializeViewModel();
        initializePermissionLauncher();
        setupProgressDrawing();
        setupClickListeners();
        observeViewModel();
    }

    private void setupViewBinding() {
        binding = ActivityHomeBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
    }

    private void configureStatusBar() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            WindowInsetsControllerCompat controller = new WindowInsetsControllerCompat(getWindow(), getWindow().getDecorView());
            controller.setAppearanceLightStatusBars(true);
        }
    }

    private void initializeViewModel() {
        viewModel = new ViewModelProvider(this).get(HomeViewModel.class);
    }

    private void initializePermissionLauncher() {
        permissionLauncher = registerForActivityResult(new ActivityResultContracts.RequestMultiplePermissions(), result -> {
            boolean allGranted = true;
            for (Boolean granted : result.values()) {
                if (!granted) {
                    allGranted = false;
                    break;
                }
            }

            if (allGranted) {
                if (pendingActivityClass != null) {
                    navigateToActivity(pendingActivityClass);
                    pendingActivityClass = null;
                }
            } else {
                Toast.makeText(this, "Permission denied. Some features may not work properly.", Toast.LENGTH_SHORT).show();
            }
        });
    }

    // ==================== UI Setup Methods ====================

    private void setupClickListeners() {
        setupSettingsButton();
        setupScanButton();
        setupFeatureCards();
    }

    private void setupSettingsButton() {
        ImageView settingsButton = binding.btnSettings;
        settingsButton.setOnClickListener(v -> navigateToActivity(SettingsActivity.class));
    }

    private void setupScanButton() {
        binding.btnScanNow.setOnClickListener(v ->
            requestPermissionsAndNavigate(CleanupScanActivity.class, Screen.JUNK_SCAN));
    }

    private void setupFeatureCards() {
        binding.cardJunk.setOnClickListener(v ->
            requestPermissionsAndNavigate(CleanupScanActivity.class, Screen.JUNK_SCAN));

        binding.cardImages.setOnClickListener(v ->
            requestPermissionsAndNavigate(ImageFilesActivity.class, Screen.IMAGES));

        binding.cardAudios.setOnClickListener(v ->
            requestPermissionsAndNavigate(AudioFilesActivity.class, Screen.AUDIOS));

        binding.cardVideos.setOnClickListener(v ->
            requestPermissionsAndNavigate(VideoFilesActivity.class, Screen.VIDEOS));

        binding.cardBrowser.setOnClickListener(v ->
            startActivity(new Intent(this, PrivateBrowserActivity.class)));
    }

    // ==================== Navigation Methods ====================

    private <T> void navigateToActivity(Class<T> activityClass) {
        Intent intent = new Intent(this, activityClass);
        startActivity(intent);
    }

    // ==================== Permission Handling Methods ====================

    private <T> void requestPermissionsAndNavigate(Class<T> activityClass, Screen screen) {
        pendingActivityClass = activityClass;

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            determineRequiredPermissionsForAndroid13Plus(screen, activityClass);
        } else {
            requiredPermissions = new String[]{Manifest.permission.READ_EXTERNAL_STORAGE};
        }

        if (hasAllRequiredPermissions()) {
            navigateToActivity(activityClass);
        } else {
            displayPermissionDialog(screen);
        }
    }

    private <T> void determineRequiredPermissionsForAndroid13Plus(Screen screen, Class<T> activityClass) {
        switch (screen) {
            case IMAGES:
                requiredPermissions = new String[]{Manifest.permission.READ_MEDIA_IMAGES};
                break;
            case AUDIOS:
                requiredPermissions = new String[]{Manifest.permission.READ_MEDIA_AUDIO};
                break;
            case VIDEOS:
                requiredPermissions = new String[]{Manifest.permission.READ_MEDIA_VIDEO};
                break;
            case LARGE_FILES:
            case JUNK_SCAN:
                requiredPermissions = new String[]{
                    Manifest.permission.READ_MEDIA_IMAGES,
                    Manifest.permission.READ_MEDIA_VIDEO,
                    Manifest.permission.READ_MEDIA_AUDIO
                };
                break;
            default:
                navigateToActivity(activityClass);
                return;
        }
    }

    private boolean hasAllRequiredPermissions() {
        for (String permission : requiredPermissions) {
            if (ContextCompat.checkSelfPermission(this, permission) != PackageManager.PERMISSION_GRANTED) {
                return false;
            }
        }
        return true;
    }

    private void displayPermissionDialog(Screen screen) {
        Dialog dialog = createPermissionDialog();
        setupPermissionDialogContent(dialog);
        setupPermissionDialogButtons(dialog);
        dialog.show();
    }

    private Dialog createPermissionDialog() {
        Dialog dialog = new Dialog(this, R.style.TransparentDialog);
        dialog.setContentView(R.layout.dialog_permission);
        dialog.setCancelable(true);
        dialog.getWindow().setBackgroundDrawableResource(android.R.color.transparent);
        return dialog;
    }

    private void setupPermissionDialogContent(Dialog dialog) {
        TextView messageText = dialog.findViewById(R.id.tv_permission_message);
        messageText.setText("Permission is denied.\nPlease authorize the phone settings.");
    }

    private void setupPermissionDialogButtons(Dialog dialog) {
        Button cancelButton = dialog.findViewById(R.id.btn_cancel);
        Button confirmButton = dialog.findViewById(R.id.btn_confirm);

        cancelButton.setOnClickListener(v -> dialog.dismiss());
        confirmButton.setOnClickListener(v -> {
            dialog.dismiss();
            permissionLauncher.launch(requiredPermissions);
        });
    }

    // ==================== ViewModel Observation Methods ====================

    private void observeViewModel() {
        observeUiState();
    }

    private void observeUiState() {
        viewModel.getUiState().observe(this, state -> {
            displayStorageInformation(state.getUsagePercentage(), state.getStorageUsedGb(), state.getStorageTotalGb());
            updateScanButtonState(!state.isScanning());
        });
    }

    private void updateScanButtonState(boolean enabled) {
        binding.btnScanNow.setEnabled(enabled);
    }

    // ==================== Storage Display Methods ====================

    private void displayStorageInformation(float usagePercentage, int usedGb, int totalGb) {
        float targetSweepAngle = calculateTargetSweepAngle(usagePercentage);
        resetProgressAndStartAnimation(targetSweepAngle);
        updateStorageTexts(usedGb, totalGb);
    }

    private float calculateTargetSweepAngle(float usagePercentage) {
        return (usagePercentage / 100f) * 360f;
    }

    private void resetProgressAndStartAnimation(float targetSweepAngle) {
        currentSweepAngle = 0f;
        animateStorageProgress(targetSweepAngle);
    }

    private void updateStorageTexts(int usedGb, int totalGb) {
        binding.tvStorageUsed.setText(usedGb + "GB / " + totalGb + "GB");
        binding.tvStoragePercentage.setText("0");
    }

    // ==================== Animation Methods ====================

    private void animateStorageProgress(float targetSweepAngle) {
        cancelExistingAnimation();
        createAndStartProgressAnimation(targetSweepAngle);
    }

    private void cancelExistingAnimation() {
        if (progressAnimator != null && progressAnimator.isRunning()) {
            progressAnimator.cancel();
        }
    }

    private void createAndStartProgressAnimation(float targetSweepAngle) {
        isAnimating = true;

        progressAnimator = ValueAnimator.ofFloat(0f, targetSweepAngle);
        progressAnimator.setDuration(1500);
        progressAnimator.setInterpolator(new LinearInterpolator());

        setupAnimationUpdateListener();
        setupAnimationLifecycleListener(targetSweepAngle);

        progressAnimator.start();
    }

    private void setupAnimationUpdateListener() {
        progressAnimator.addUpdateListener(animation -> {
            currentSweepAngle = (float) animation.getAnimatedValue();
            binding.progressArc.invalidate();

            updatePercentageDisplay();
            updateProgressCapPosition(currentSweepAngle);
        });
    }

    private void updatePercentageDisplay() {
        int displayPercentage = Math.round((currentSweepAngle / 360f) * 100f);
        binding.tvStoragePercentage.setText(String.valueOf(displayPercentage));
    }

    private void setupAnimationLifecycleListener(float targetSweepAngle) {
        progressAnimator.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                isAnimating = false;
                int finalPercentage = Math.round((targetSweepAngle / 360f) * 100f);
                binding.tvStoragePercentage.setText(String.valueOf(finalPercentage));
            }
        });
    }

    // ==================== Progress Drawing Methods ====================

    private void setupProgressDrawing() {
        initializeProgressPaint();
        initializeProgressRect();
        configureProgressView();
        setupCustomDrawListener();
    }

    private void initializeProgressPaint() {
        progressPaint = new Paint();
        progressPaint.setStyle(Paint.Style.STROKE);
        progressPaint.setStrokeWidth(60);
        progressPaint.setAntiAlias(true);
    }

    private void initializeProgressRect() {
        progressRect = new RectF();
    }

    private void configureProgressView() {
        binding.progressArc.setLayerType(View.LAYER_TYPE_HARDWARE, null);
    }

    private void setupCustomDrawListener() {
        if (binding.progressArc instanceof CustomDrawView) {
            CustomDrawView customView = (CustomDrawView) binding.progressArc;
            customView.setOnDrawListener(this::drawProgressArc);
        }
    }

    private void drawProgressArc(android.graphics.Canvas canvas) {
        int width = binding.progressArc.getWidth();
        int height = binding.progressArc.getHeight();

        calculateProgressRect(width, height);
        drawBackgroundTrack(canvas);
        drawProgressGradient(canvas, width, height);

        if (currentSweepAngle > 0) {
            updateProgressCapPosition(currentSweepAngle);
        }
    }

    private void calculateProgressRect(int width, int height) {
        float padding = progressPaint.getStrokeWidth() / 2 + 40;
        progressRect.set(padding, padding, width - padding, height - padding);
    }

    private void drawBackgroundTrack(android.graphics.Canvas canvas) {
        Paint trackPaint = new Paint(progressPaint);
        trackPaint.setColor(Color.parseColor("#edf9f5"));
        trackPaint.setAlpha(200);
        canvas.drawArc(progressRect, 0, 360, false, trackPaint);
    }

    private void drawProgressGradient(android.graphics.Canvas canvas, int width, int height) {
        Paint gradientPaint = new Paint(progressPaint);
        gradientPaint.setShader(new android.graphics.LinearGradient(
            0, 0, width, height,
            new int[]{Color.parseColor("#f1f8f6"), Color.parseColor("#10A963")},
            null, android.graphics.Shader.TileMode.CLAMP));
        canvas.drawArc(progressRect, -90, currentSweepAngle, false, gradientPaint);
    }

    private void updateProgressCapPosition(float sweepAngle) {
        if (!shouldShowProgressCap(sweepAngle)) {
            return;
        }

        if (!areViewDimensionsValid()) {
            Log.w("HomeActivity", "updateProgressCapPosition: View dimensions or paint not ready.");
            return;
        }

        CapPositionCalculation calculation = calculateCapPosition(sweepAngle);
        if (calculation.isValid()) {
            applyCapPosition(calculation);
        }
    }

    private boolean shouldShowProgressCap(float sweepAngle) {
        if (sweepAngle <= 0.1f) {
            if (binding.progressCap.getVisibility() != View.INVISIBLE) {
                binding.progressCap.setVisibility(View.INVISIBLE);
            }
            return false;
        }
        return true;
    }

    private boolean areViewDimensionsValid() {
        int viewWidth = binding.progressArc.getWidth();
        int viewHeight = binding.progressArc.getHeight();
        return viewWidth > 0 && viewHeight > 0 && progressPaint != null;
    }

    private CapPositionCalculation calculateCapPosition(float sweepAngle) {
        int viewWidth = binding.progressArc.getWidth();
        int viewHeight = binding.progressArc.getHeight();

        float strokeWidth = progressPaint.getStrokeWidth();
        float padding = strokeWidth / 2f + 40f;

        float centerX = viewWidth / 2f;
        float centerY = viewHeight / 2f;
        float radius = (viewWidth - 2 * padding) / 2f;

        if (radius <= 0) {
            Log.w("HomeActivity", "Invalid radius calculated: " + radius);
            return new CapPositionCalculation();
        }

        float angleDegrees = -90f + sweepAngle;
        float angleRadians = (float) Math.toRadians(angleDegrees);

        float capTargetCenterX = centerX + radius * (float) Math.cos(angleRadians);
        float capTargetCenterY = centerY + radius * (float) Math.sin(angleRadians);

        return new CapPositionCalculation(capTargetCenterX, capTargetCenterY, true);
    }

    private void applyCapPosition(CapPositionCalculation calculation) {
        int capWidth = binding.progressCap.getWidth();
        int capHeight = binding.progressCap.getHeight();
        float density = getResources().getDisplayMetrics().density;

        if (capWidth <= 0) capWidth = (int) (20 * density);
        if (capHeight <= 0) capHeight = (int) (20 * density);

        float capX = calculation.centerX - capWidth / 2f;
        float capY = calculation.centerY - capHeight / 2f;

        binding.progressCap.setX(capX);
        binding.progressCap.setY(capY);
        binding.progressCap.bringToFront();
    }

    private static class CapPositionCalculation {
        final float centerX;
        final float centerY;
        final boolean valid;

        CapPositionCalculation() {
            this(0, 0, false);
        }

        CapPositionCalculation(float centerX, float centerY, boolean valid) {
            this.centerX = centerX;
            this.centerY = centerY;
            this.valid = valid;
        }

        boolean isValid() {
            return valid;
        }
    }

    // ==================== Lifecycle Management Methods ====================

    private void refreshStorageDisplay() {
        currentSweepAngle = 0f;
        HomeViewModel.MainScreenState state = viewModel.getUiState().getValue();
        if (state != null) {
            displayStorageInformation(state.getUsagePercentage(), state.getStorageUsedGb(), state.getStorageTotalGb());
        }
    }

    private void cleanupResources() {
        if (progressAnimator != null) {
            progressAnimator.cancel();
        }
        binding = null;
    }
}
