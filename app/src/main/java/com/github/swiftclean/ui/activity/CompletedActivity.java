package com.github.swiftclean.ui.activity;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;

import androidx.annotation.Nullable;

import com.github.swiftclean.R;
import com.github.swiftclean.databinding.ActivityCompletedBinding;

/**
 * 清理完成页面
 */
public class CompletedActivity extends BaseActivity implements View.OnClickListener {

    private ActivityCompletedBinding binding;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityCompletedBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        // 初始化工具栏
        setupToolbar(binding.toolbar);

        // 设置点击事件
        setupClickListeners();
    }


    private void setupClickListeners() {
        binding.btnJunkManage.setOnClickListener(this);
        binding.btnVideoManage.setOnClickListener(this);
        binding.btnImagesManage.setOnClickListener(this);
        binding.btnAudiosManage.setOnClickListener(this);
        binding.btnBrowserManage.setOnClickListener(this);

    }

    @Override
    public void onClick(View v) {
        int id = v.getId();

        if (id == R.id.btn_junk_manage) {
            startActivity(new Intent(this, JunkScanActivity.class));
        }
//        else if (id == R.id.btn_large_files_manage) {
//            // 跳转到大文件页面
//            startActivity(new Intent(this, LargeFilesActivity.class));
//        }
        else if (id == R.id.btn_images_manage) {
            // 跳转到图片页面
            startActivity(new Intent(this, ImagesActivity.class));
        } else if (id == R.id.btn_video_manage) {
            startActivity(new Intent(this, VideosActivity.class));
        } else if (id == R.id.btn_audios_manage) {
            // 跳转到音频页面
            startActivity(new Intent(this, AudiosActivity.class));
        } else if (id == R.id.btn_browser_manage) {
            startActivity(new Intent(this, SecureBrowserActivity.class));
        }
    }
}
