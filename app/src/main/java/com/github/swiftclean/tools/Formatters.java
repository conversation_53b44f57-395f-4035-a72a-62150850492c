package com.github.swiftclean.tools;

import java.text.NumberFormat;
import java.util.Locale;
import java.util.concurrent.TimeUnit;

/**
 * Java version of Formatters utility class
 * Provides formatting utilities for file sizes and durations
 */
public class Formatters {
    
    /**
     * Format file size in bytes to a human-readable string (B, KB, MB, GB, TB)
     * @param bytes the size in bytes
     * @return formatted size string
     */
    public static String formatStorageSize(long bytes) {
        if (bytes < 0) return "0 B";
        
        String[] units = {"B", "KB", "MB", "GB", "TB"};
        double size = bytes;
        int unitIndex = 0;
        
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        
        NumberFormat format = NumberFormat.getNumberInstance(Locale.US);
        format.setMaximumFractionDigits(unitIndex < 2 ? 0 : 1);
        
        return format.format(size) + " " + units[unitIndex];
    }
    
    /**
     * Format duration in milliseconds to a human-readable string (MM:SS or HH:MM:SS)
     * @param millis the duration in milliseconds
     * @return formatted duration string
     */
    public static String formatDuration(Long millis) {
        if (millis == null || millis < 0) return "--:--";
        
        long hours = TimeUnit.MILLISECONDS.toHours(millis);
        long minutes = TimeUnit.MILLISECONDS.toMinutes(millis) % TimeUnit.HOURS.toMinutes(1);
        long seconds = TimeUnit.MILLISECONDS.toSeconds(millis) % TimeUnit.MINUTES.toSeconds(1);
        
        if (hours > 0) {
            return String.format("%02d:%02d:%02d", hours, minutes, seconds);
        } else {
            return String.format("%02d:%02d", minutes, seconds);
        }
    }
}
