package com.github.swiftclean.tools;

import java.text.NumberFormat;
import java.util.Locale;
import java.util.concurrent.TimeUnit;

/**
 * Display Formatters - Utility class for formatting data for display
 * Provides consistent formatting for file sizes, durations, and other display values
 */
public class DisplayFormatters {
    
    // ==================== Storage Size Formatting ====================
    
    /**
     * Format file size in bytes to human-readable string (B, KB, MB, GB, TB)
     * @param bytes the size in bytes
     * @return formatted size string with appropriate unit
     */
    public static String formatStorageSize(long bytes) {
        if (bytes < 0) return "0 B";
        
        StorageSizeUnit unit = determineOptimalStorageUnit(bytes);
        double convertedSize = convertBytesToUnit(bytes, unit);
        
        return formatSizeWithUnit(convertedSize, unit);
    }
    
    /**
     * Format storage size with specific precision
     * @param bytes the size in bytes
     * @param decimalPlaces number of decimal places to show
     * @return formatted size string
     */
    public static String formatStorageSizeWithPrecision(long bytes, int decimalPlaces) {
        if (bytes < 0) return "0 B";
        
        StorageSizeUnit unit = determineOptimalStorageUnit(bytes);
        double convertedSize = convertBytesToUnit(bytes, unit);
        
        NumberFormat format = createNumberFormat(decimalPlaces);
        return format.format(convertedSize) + " " + unit.getSymbol();
    }
    
    private static StorageSizeUnit determineOptimalStorageUnit(long bytes) {
        StorageSizeUnit[] units = StorageSizeUnit.values();
        
        for (int i = units.length - 1; i >= 0; i--) {
            if (bytes >= units[i].getBytes()) {
                return units[i];
            }
        }
        
        return StorageSizeUnit.BYTES;
    }
    
    private static double convertBytesToUnit(long bytes, StorageSizeUnit unit) {
        return (double) bytes / unit.getBytes();
    }
    
    private static String formatSizeWithUnit(double size, StorageSizeUnit unit) {
        NumberFormat format = createNumberFormat(unit.getDefaultDecimalPlaces());
        return format.format(size) + " " + unit.getSymbol();
    }
    
    private static NumberFormat createNumberFormat(int decimalPlaces) {
        NumberFormat format = NumberFormat.getNumberInstance(Locale.US);
        format.setMaximumFractionDigits(decimalPlaces);
        format.setMinimumFractionDigits(0);
        return format;
    }
    
    // ==================== Duration Formatting ====================
    
    /**
     * Format duration in milliseconds to human-readable string (MM:SS or HH:MM:SS)
     * @param millis the duration in milliseconds
     * @return formatted duration string
     */
    public static String formatDuration(Long millis) {
        if (millis == null || millis < 0) return "--:--";
        
        DurationComponents duration = extractDurationComponents(millis);
        
        return duration.hasHours() ? 
            formatDurationWithHours(duration) : 
            formatDurationMinutesSeconds(duration);
    }
    
    /**
     * Format duration with custom format
     * @param millis duration in milliseconds
     * @param includeHours whether to always include hours
     * @return formatted duration string
     */
    public static String formatDurationCustom(Long millis, boolean includeHours) {
        if (millis == null || millis < 0) return "--:--:--";
        
        DurationComponents duration = extractDurationComponents(millis);
        
        return includeHours ? 
            formatDurationWithHours(duration) : 
            formatDurationMinutesSeconds(duration);
    }
    
    private static DurationComponents extractDurationComponents(long millis) {
        long hours = TimeUnit.MILLISECONDS.toHours(millis);
        long minutes = TimeUnit.MILLISECONDS.toMinutes(millis) % TimeUnit.HOURS.toMinutes(1);
        long seconds = TimeUnit.MILLISECONDS.toSeconds(millis) % TimeUnit.MINUTES.toSeconds(1);
        
        return new DurationComponents(hours, minutes, seconds);
    }
    
    private static String formatDurationWithHours(DurationComponents duration) {
        return String.format("%02d:%02d:%02d", 
            duration.hours, duration.minutes, duration.seconds);
    }
    
    private static String formatDurationMinutesSeconds(DurationComponents duration) {
        return String.format("%02d:%02d", duration.minutes, duration.seconds);
    }
    
    // ==================== Percentage Formatting ====================
    
    /**
     * Format percentage value with specified decimal places
     * @param percentage the percentage value (0-100)
     * @param decimalPlaces number of decimal places
     * @return formatted percentage string
     */
    public static String formatPercentage(float percentage, int decimalPlaces) {
        NumberFormat format = createNumberFormat(decimalPlaces);
        return format.format(percentage) + "%";
    }
    
    /**
     * Format percentage value with default formatting (1 decimal place)
     * @param percentage the percentage value (0-100)
     * @return formatted percentage string
     */
    public static String formatPercentage(float percentage) {
        return formatPercentage(percentage, 1);
    }
    
    // ==================== Helper Classes ====================
    
    /**
     * Storage size units with their byte values and display properties
     */
    private enum StorageSizeUnit {
        BYTES(1L, "B", 0),
        KILOBYTES(1024L, "KB", 0),
        MEGABYTES(1024L * 1024L, "MB", 1),
        GIGABYTES(1024L * 1024L * 1024L, "GB", 1),
        TERABYTES(1024L * 1024L * 1024L * 1024L, "TB", 2);
        
        private final long bytes;
        private final String symbol;
        private final int defaultDecimalPlaces;
        
        StorageSizeUnit(long bytes, String symbol, int defaultDecimalPlaces) {
            this.bytes = bytes;
            this.symbol = symbol;
            this.defaultDecimalPlaces = defaultDecimalPlaces;
        }
        
        public long getBytes() { return bytes; }
        public String getSymbol() { return symbol; }
        public int getDefaultDecimalPlaces() { return defaultDecimalPlaces; }
    }
    
    /**
     * Duration components holder
     */
    private static class DurationComponents {
        final long hours;
        final long minutes;
        final long seconds;
        
        DurationComponents(long hours, long minutes, long seconds) {
            this.hours = hours;
            this.minutes = minutes;
            this.seconds = seconds;
        }
        
        boolean hasHours() {
            return hours > 0;
        }
    }
}
