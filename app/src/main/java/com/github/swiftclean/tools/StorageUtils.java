package com.github.swiftclean.tools;

import android.content.Context;
import android.os.Environment;
import android.os.StatFs;

import java.io.File;
import java.text.DecimalFormat;

/**
 * Storage Utilities - Utility class for storage and file operations
 * Provides methods for storage analysis, file size calculations, and storage management
 */
public class StorageUtils {

    private StorageUtils() {
        // Private constructor to prevent instantiation
    }

    // ==================== File Size Formatting ====================

    /**
     * Format file size in bytes to human-readable format
     * @param bytes Size in bytes
     * @return Formatted size string (e.g., "1.5 MB")
     */
    public static String formatFileSize(long bytes) {
        if (bytes <= 0) {
            return "0 B";
        }
        
        final String[] units = new String[] { "B", "KB", "MB", "GB", "TB" };
        int digitGroups = (int) (Math.log10(bytes) / Math.log10(1024));
        
        // Limit to available units
        digitGroups = Math.min(digitGroups, units.length - 1);
        
        DecimalFormat df = new DecimalFormat("#,##0.#");
        return df.format(bytes / Math.pow(1024, digitGroups)) + " " + units[digitGroups];
    }

    /**
     * Format file size with specific decimal places
     * @param bytes Size in bytes
     * @param decimalPlaces Number of decimal places to show
     * @return Formatted size string
     */
    public static String formatFileSizeWithPrecision(long bytes, int decimalPlaces) {
        if (bytes <= 0) {
            return "0 B";
        }
        
        final String[] units = new String[] { "B", "KB", "MB", "GB", "TB" };
        int digitGroups = (int) (Math.log10(bytes) / Math.log10(1024));
        digitGroups = Math.min(digitGroups, units.length - 1);
        
        String pattern = "#,##0" + (decimalPlaces > 0 ? "." + "0".repeat(decimalPlaces) : "");
        DecimalFormat df = new DecimalFormat(pattern);
        
        return df.format(bytes / Math.pow(1024, digitGroups)) + " " + units[digitGroups];
    }

    // ==================== Storage Information ====================

    /**
     * Get storage information for the external storage
     * @return StorageInfo object containing storage details
     */
    public static StorageInfo getExternalStorageInfo() {
        try {
            File externalStorageDirectory = Environment.getExternalStorageDirectory();
            return getStorageInfoForPath(externalStorageDirectory.getPath());
        } catch (Exception e) {
            android.util.Log.e("StorageUtils", "Error getting external storage info", e);
            return new StorageInfo(0, 0, 0, 0.0f);
        }
    }

    /**
     * Get storage information for a specific path
     * @param path The path to analyze
     * @return StorageInfo object containing storage details
     */
    public static StorageInfo getStorageInfoForPath(String path) {
        try {
            StatFs statFs = new StatFs(path);
            
            long blockSize = statFs.getBlockSizeLong();
            long totalBlocks = statFs.getBlockCountLong();
            long availableBlocks = statFs.getAvailableBlocksLong();
            
            long totalBytes = totalBlocks * blockSize;
            long availableBytes = availableBlocks * blockSize;
            long usedBytes = totalBytes - availableBytes;
            
            float usagePercentage = totalBytes > 0 ? 
                ((float) usedBytes / totalBytes) * 100f : 0f;
            
            return new StorageInfo(totalBytes, usedBytes, availableBytes, usagePercentage);
        } catch (Exception e) {
            android.util.Log.e("StorageUtils", "Error getting storage info for path: " + path, e);
            return new StorageInfo(0, 0, 0, 0.0f);
        }
    }

    /**
     * Check if external storage is available and writable
     * @return true if external storage is available and writable
     */
    public static boolean isExternalStorageWritable() {
        String state = Environment.getExternalStorageState();
        return Environment.MEDIA_MOUNTED.equals(state);
    }

    /**
     * Check if external storage is available for reading
     * @return true if external storage is available for reading
     */
    public static boolean isExternalStorageReadable() {
        String state = Environment.getExternalStorageState();
        return Environment.MEDIA_MOUNTED.equals(state) ||
               Environment.MEDIA_MOUNTED_READ_ONLY.equals(state);
    }

    // ==================== File Operations ====================

    /**
     * Calculate the total size of a directory
     * @param directory The directory to calculate size for
     * @return Total size in bytes
     */
    public static long calculateDirectorySize(File directory) {
        if (directory == null || !directory.exists() || !directory.isDirectory()) {
            return 0;
        }
        
        long totalSize = 0;
        File[] files = directory.listFiles();
        
        if (files != null) {
            for (File file : files) {
                if (file.isDirectory()) {
                    totalSize += calculateDirectorySize(file);
                } else {
                    totalSize += file.length();
                }
            }
        }
        
        return totalSize;
    }

    /**
     * Get the number of files in a directory (recursive)
     * @param directory The directory to count files in
     * @return Number of files
     */
    public static int countFilesInDirectory(File directory) {
        if (directory == null || !directory.exists() || !directory.isDirectory()) {
            return 0;
        }
        
        int fileCount = 0;
        File[] files = directory.listFiles();
        
        if (files != null) {
            for (File file : files) {
                if (file.isDirectory()) {
                    fileCount += countFilesInDirectory(file);
                } else {
                    fileCount++;
                }
            }
        }
        
        return fileCount;
    }

    /**
     * Convert bytes to megabytes
     * @param bytes Size in bytes
     * @return Size in megabytes
     */
    public static double bytesToMB(long bytes) {
        return bytes / (1024.0 * 1024.0);
    }

    /**
     * Convert bytes to gigabytes
     * @param bytes Size in bytes
     * @return Size in gigabytes
     */
    public static double bytesToGB(long bytes) {
        return bytes / (1024.0 * 1024.0 * 1024.0);
    }

    /**
     * Convert megabytes to bytes
     * @param mb Size in megabytes
     * @return Size in bytes
     */
    public static long mbToBytes(double mb) {
        return (long) (mb * 1024 * 1024);
    }

    /**
     * Convert gigabytes to bytes
     * @param gb Size in gigabytes
     * @return Size in bytes
     */
    public static long gbToBytes(double gb) {
        return (long) (gb * 1024 * 1024 * 1024);
    }

    // ==================== Data Classes ====================

    /**
     * Storage information data class
     */
    public static class StorageInfo {
        private final long totalBytes;
        private final long usedBytes;
        private final long availableBytes;
        private final float usagePercentage;

        public StorageInfo(long totalBytes, long usedBytes, long availableBytes, float usagePercentage) {
            this.totalBytes = totalBytes;
            this.usedBytes = usedBytes;
            this.availableBytes = availableBytes;
            this.usagePercentage = Math.max(0f, Math.min(100f, usagePercentage));
        }

        public long getTotalBytes() { return totalBytes; }
        public long getUsedBytes() { return usedBytes; }
        public long getAvailableBytes() { return availableBytes; }
        public float getUsagePercentage() { return usagePercentage; }

        public int getTotalGB() { return (int) bytesToGB(totalBytes); }
        public int getUsedGB() { return (int) bytesToGB(usedBytes); }
        public int getAvailableGB() { return (int) bytesToGB(availableBytes); }

        public String getFormattedTotal() { return formatFileSize(totalBytes); }
        public String getFormattedUsed() { return formatFileSize(usedBytes); }
        public String getFormattedAvailable() { return formatFileSize(availableBytes); }

        @Override
        public String toString() {
            return String.format("StorageInfo{total=%s, used=%s, available=%s, usage=%.1f%%}",
                getFormattedTotal(), getFormattedUsed(), getFormattedAvailable(), usagePercentage);
        }
    }
}
