package com.github.swiftclean.tools;

import java.text.DecimalFormat;

/**
 * Utility class for file operations
 */
public class FileUtils {

    private FileUtils() {
        // Private constructor to prevent instantiation
    }

    /**
     * Format file size in bytes to human-readable format
     * @param bytes Size in bytes
     * @return Formatted size string (e.g., "1.5 MB")
     */
    public static String formatSize(long bytes) {
        if (bytes <= 0) {
            return "0 B";
        }
        
        final String[] units = new String[] { "B", "KB", "MB", "GB", "TB" };
        int digitGroups = (int) (Math.log10(bytes) / Math.log10(1024));
        
        // Limit to available units
        digitGroups = Math.min(digitGroups, units.length - 1);
        
        DecimalFormat df = new DecimalFormat("#,##0.#");
        return df.format(bytes / Math.pow(1024, digitGroups)) + " " + units[digitGroups];
    }
}
