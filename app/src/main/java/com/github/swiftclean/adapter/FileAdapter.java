package com.github.swiftclean.adapter;

import android.content.Context;
import android.net.Uri;
import android.provider.MediaStore;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.request.RequestOptions;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.github.swiftclean.R;
import com.github.swiftclean.bean.SelectableFile;
import com.github.swiftclean.tools.StorageUtils;

import java.util.ArrayList;
import java.util.List;

public class FileAdapter extends RecyclerView.Adapter<FileAdapter.FileViewHolder> {

    private final List<SelectableFile> files;
    private final OnFileItemClickListener listener;

    public FileAdapter(OnFileItemClickListener listener) {
        this.files = new ArrayList<>();
        this.listener = listener;
    }

    @NonNull
    @Override
    public FileViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_file, parent, false);
        return new FileViewHolder(view, listener);
    }

    @Override
    public void onBindViewHolder(@NonNull FileViewHolder holder, int position) {
        SelectableFile file = files.get(position);
        holder.bind(file);
    }

    @Override
    public int getItemCount() {
        return files.size();
    }

    public void updateFiles(List<SelectableFile> newFiles) {
        files.clear();
        if (newFiles != null) {
            files.addAll(newFiles);
        }
        notifyDataSetChanged();
    }

    public List<SelectableFile> getFiles() {
        return files;
    }

    public interface OnFileItemClickListener {
        void onFileItemClick(SelectableFile file);
    }

    static class FileViewHolder extends RecyclerView.ViewHolder {
        private final ImageView fileIcon;
        private final ImageView fileThumbnail;
        private final TextView fileName;
        private final TextView fileSize;
        private final CheckBox checkBox;
        private final OnFileItemClickListener listener;

        public FileViewHolder(@NonNull View itemView, OnFileItemClickListener listener) {
            super(itemView);
            this.listener = listener;
            fileIcon = itemView.findViewById(R.id.file_icon);
            fileThumbnail = itemView.findViewById(R.id.file_thumbnail);
            fileName = itemView.findViewById(R.id.file_name);
            fileSize = itemView.findViewById(R.id.file_size);
            checkBox = itemView.findViewById(R.id.checkbox);

            // 设置整个项的点击监听器
            itemView.setOnClickListener(v -> {
                int position = getAdapterPosition();
                if (position != RecyclerView.NO_POSITION && listener != null) {
                    listener.onFileItemClick(getFileAtPosition(position));
                }
            });

            // 设置复选框的点击监听器
            checkBox.setOnClickListener(v -> {
                int position = getAdapterPosition();
                if (position != RecyclerView.NO_POSITION && listener != null) {
                    listener.onFileItemClick(getFileAtPosition(position));
                }
            });
        }

        private SelectableFile getFileAtPosition(int position) {
            return ((FileAdapter) getBindingAdapter()).files.get(position);
        }

        public void bind(SelectableFile file) {
            fileName.setText(file.getName());
            fileSize.setText(StorageUtils.formatFileSize(file.getSizeBytes()));

            // 设置选中状态
            boolean isSelected = file.isSelected();
            checkBox.setChecked(isSelected);

            // 根据选中状态设置项目背景
//            itemView.setBackgroundResource(isSelected ?
//                    R.drawable.selected_item_background :
//                    android.R.drawable.list_selector_background);

            // 根据文件类型设置图标或缩略图
            setFileIconOrThumbnail(file);
        }

        private void setFileIconOrThumbnail(SelectableFile file) {
            Context context = itemView.getContext();
            int mediaType = file.getMediaType();
            String mimeType = file.getMimeType();
            Uri contentUri = Uri.parse(file.getUri());

            // 默认隐藏缩略图，显示图标
            fileThumbnail.setVisibility(View.GONE);
            fileIcon.setVisibility(View.VISIBLE);

            if (mediaType == MediaStore.Files.FileColumns.MEDIA_TYPE_IMAGE ||
                    (mimeType != null && mimeType.startsWith("image/"))) {
                // 加载图片缩略图
                loadThumbnail(contentUri);
                fileIcon.setImageResource(R.drawable.ic_image_file);
            } else if (mediaType == MediaStore.Files.FileColumns.MEDIA_TYPE_VIDEO ||
                    (mimeType != null && mimeType.startsWith("video/"))) {
                // 加载视频缩略图
                loadVideoThumbnail(contentUri);
                fileIcon.setImageResource(R.drawable.ic_video_file);
            } else if (mediaType == MediaStore.Files.FileColumns.MEDIA_TYPE_AUDIO ||
                    (mimeType != null && mimeType.startsWith("audio/"))) {
                fileIcon.setImageResource(R.drawable.ic_audio_file);
            } else {
                fileIcon.setImageResource(R.drawable.ic_document_file);
            }
        }

        private void loadThumbnail(Uri uri) {
            Context context = itemView.getContext();

            RequestOptions options = new RequestOptions()
                    .centerCrop()
                    .diskCacheStrategy(DiskCacheStrategy.ALL);

            Glide.with(context)
                    .load(uri)
                    .apply(options)
                    .into(fileThumbnail);

            // 显示缩略图，隐藏图标
            fileThumbnail.setVisibility(View.VISIBLE);
            fileIcon.setVisibility(View.GONE);
        }

        private void loadVideoThumbnail(Uri uri) {
            Context context = itemView.getContext();

            RequestOptions options = new RequestOptions()
                    .centerCrop()
                    .diskCacheStrategy(DiskCacheStrategy.ALL);

            Glide.with(context)
                    .load(uri)
                    .apply(options)
                    .into(fileThumbnail);

            // 显示缩略图，隐藏图标
            fileThumbnail.setVisibility(View.VISIBLE);
            fileIcon.setVisibility(View.GONE);
        }
    }
}
