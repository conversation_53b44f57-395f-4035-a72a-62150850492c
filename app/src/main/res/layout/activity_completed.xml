<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    android:fitsSystemWindows="true"
    tools:context=".ui.activity.CompletedActivity">

    <!-- Top App Bar -->
    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBarLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@android:color/transparent"
        app:elevation="0dp">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/white"
            app:navigationIcon="@mipmap/ic_back"
            app:title="Cleanup Completed"
            app:titleTextColor="@color/toolbar_title_color" />

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Main Content -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:orientation="vertical">

            <!-- Spacer -->
            <Space
                android:layout_width="match_parent"
                android:layout_height="32dp" />

            <!-- Completed Image -->
            <ImageView
                android:id="@+id/completed_image"
                android:layout_width="210dp"
                android:layout_height="210dp"
                android:contentDescription="Completed"
                android:src="@mipmap/bg_completed" />

            <!-- Spacer -->
            <Space
                android:layout_width="match_parent"
                android:layout_height="32dp" />

            <!-- Status Text -->
            <TextView
                android:id="@+id/status_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:paddingHorizontal="20dp"
                android:text="The current mobile phone statushas reached the best!"
                android:textColor="#37405D"
                android:textSize="16sp" />

            <!-- Spacer -->
            <Space
                android:layout_width="match_parent"
                android:layout_height="40dp" />

            <!-- Category List Container -->
            <LinearLayout
                android:id="@+id/category_list_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="#F4F6FA"
                android:orientation="vertical"
                android:padding="16dp">

                <!-- Junk Files Category -->
                <LinearLayout
                    android:id="@+id/junk_files_category"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp">

                    <ImageView
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:contentDescription="Junk Files"
                        android:src="@drawable/ic_junk" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_weight="1"
                        android:text="Junk Removal"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <Button
                        android:id="@+id/btn_junk_manage"
                        android:layout_width="wrap_content"
                        android:layout_height="40dp"
                        android:background="@drawable/button_outline"
                        android:text="Manage"
                        android:textAllCaps="false"
                        android:textColor="@color/primary_blue" />
                </LinearLayout>

                <!-- Divider -->
                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="#F0F0F0" />

                <!-- Images Category -->
                <LinearLayout
                    android:id="@+id/images_category"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp">

                    <ImageView
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:contentDescription="Images"
                        android:src="@drawable/ic_image" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_weight="1"
                        android:text="Images"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <Button
                        android:id="@+id/btn_images_manage"
                        android:layout_width="wrap_content"
                        android:layout_height="40dp"
                        android:background="@drawable/button_outline"
                        android:text="Manage"
                        android:textAllCaps="false"
                        android:textColor="@color/primary_blue" />
                </LinearLayout>

                <!-- Divider -->
                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="#F0F0F0" />

                <!-- Videos Category -->
                <LinearLayout
                    android:id="@+id/videos_category"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:padding="16dp">

                    <ImageView
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:src="@drawable/ic_audio" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_weight="1"
                        android:text="Videos"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <Button
                        android:id="@+id/btn_video_manage"
                        android:layout_width="wrap_content"
                        android:layout_height="40dp"
                        android:background="@drawable/button_outline"
                        android:text="Manage"
                        android:textAllCaps="false"
                        android:textColor="@color/primary_blue" />
                </LinearLayout>

                <!-- Divider -->
                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="#F0F0F0" />

                <!-- Audios Category -->
                <LinearLayout
                    android:id="@+id/audios_category"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:padding="16dp">

                    <ImageView
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:src="@drawable/ic_audio" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_weight="1"
                        android:text="Audios"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <Button
                        android:id="@+id/btn_audios_manage"
                        android:layout_width="wrap_content"
                        android:layout_height="40dp"
                        android:background="@drawable/button_outline"
                        android:text="Manage"
                        android:textAllCaps="false"
                        android:textColor="@color/primary_blue" />
                </LinearLayout>

                <!-- Divider -->
                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="#F0F0F0" />

                <!-- Browser Category -->
                <LinearLayout
                    android:id="@+id/browser_category"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:padding="16dp">

                    <ImageView
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:src="@drawable/ic_audio" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_weight="1"
                        android:text="Audios"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <Button
                        android:id="@+id/btn_browser_manage"
                        android:layout_width="wrap_content"
                        android:layout_height="40dp"
                        android:background="@drawable/button_outline"
                        android:text="Manage"
                        android:textAllCaps="false"
                        android:textColor="@color/primary_blue" />
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</androidx.coordinatorlayout.widget.CoordinatorLayout>