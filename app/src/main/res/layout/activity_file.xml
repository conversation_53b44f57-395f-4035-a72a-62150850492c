<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:background="@color/background"
    tools:context=".ui.activity.FileActivity">

    <!-- Top App Bar -->
    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBarLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        app:elevation="0dp">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/white"
            app:navigationIcon="@mipmap/ic_back"
            app:title="Media Files"
            app:titleTextColor="@color/toolbar_title_color" />

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Main Content -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <!-- Loading Progress -->
        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />

        <!-- Permission Required View -->
        <LinearLayout
            android:id="@+id/permission_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="16dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="gone">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Permission Required"
                android:textColor="@color/text_primary"
                android:textSize="18sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tv_permission_message"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:gravity="center"
                android:text="Storage permission is needed to display files. Please grant the permission."
                android:textColor="@color/text_secondary" />

            <Button
                android:id="@+id/btn_grant_permission"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:background="@drawable/button_primary"
                android:paddingHorizontal="16dp"
                android:text="Grant Permission"
                android:textColor="@color/white" />
        </LinearLayout>

        <!-- Error View -->
        <LinearLayout
            android:id="@+id/error_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="16dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="gone">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Error"
                android:textColor="@color/design_default_color_error"
                android:textSize="18sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tv_error_message"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:gravity="center"
                android:text="Failed to load files."
                android:textColor="@color/text_secondary" />

            <Button
                android:id="@+id/btn_retry"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:background="@drawable/button_primary"
                android:paddingHorizontal="16dp"
                android:text="Retry"
                android:textColor="@color/white" />
        </LinearLayout>

        <!-- Empty View -->
        <LinearLayout
            android:id="@+id/empty_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="16dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="gone">

            <ImageView
                android:layout_width="200dp"
                android:layout_height="200dp"
                android:contentDescription="Empty state"
                android:src="@mipmap/bg_empty" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="Absolutely empty..."
                android:textColor="@color/text_secondary"
                android:textSize="16sp" />
        </LinearLayout>

        <!-- File List -->
        <!-- 添加足够的底部内边距，为底部栏留出空间 -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_view"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:clipToPadding="false"
            android:paddingTop="8dp"
            android:paddingBottom="80dp"
            android:visibility="gone"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:listitem="@layout/item_file"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- Bottom Selection Bar -->
    <include
        android:id="@+id/bottom_bar"
        layout="@layout/layout_selection_bottom_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:visibility="gone"
        tools:visibility="visible" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
